# Dependency Review Configuration
# https://docs.github.com/en/code-security/supply-chain-security/understanding-your-software-supply-chain/configuring-dependency-review

# Allow these licenses that are compatible with our MIT license
allow-licenses:
- MIT
- Apache-2.0
- BSD-2-Clause
- BSD-3-Clause
- ISC
- MPL-2.0
- Python-2.0
- Python-2.0.1
- "MIT AND Python-2.0.1"
- Unlicense
- GPL-3.0
- LGPL-2.1

# Fail on high severity vulnerabilities
fail-on-severity: high

# Allow these vulnerability severities
allow-vulnerabilities:
- low
- moderate

# Comment on PRs with dependency review results
comment-summary-in-pr: true
